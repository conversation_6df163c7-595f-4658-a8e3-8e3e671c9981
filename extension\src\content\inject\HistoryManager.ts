
import { ChatHistoryWithPlatformAndPrompt } from '@/common/database/dexie'
import { chatHistoryDatabaseProxy } from '@/common/service/databaseProxy'
import { HistoryBubble } from '@/content/inject/HistoryBubbleInject'

/**
 * 历史管理类
 * 从base.ts中提取的历史记录管理相关方法
 */
export class HistoryManager {
  private historyBubble: HistoryBubble | null = null

  constructor() {
    this.initializeHistoryBubble()
    this.setupEventListeners()
  }

  /**
   * 初始化历史气泡
   */
  private initializeHistoryBubble(): void {
    if (this.historyBubble) return

    this.historyBubble = new HistoryBubble({
      maxItems: 10,
      maxWidth: 320,
      showPlatformIcons: true
    })

    console.log('History bubble initialized')
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听历史项点击事件
    document.addEventListener('echosync:history-item-click', (event: any) => {
      const { chat } = event.detail
      this.handleHistoryItemClick(chat)
    })

    // 监听请求历史数据事件
    document.addEventListener('echosync:request-history-data', () => {
      this.showHistoryBubble()
    })

    console.log('History event listeners set up')
  }

  /**
   * 显示历史气泡
   */
  async showHistoryBubble(): Promise<void> {
    if (!this.historyBubble) return

    try {
      // 获取去重的聊天历史
      const result = await chatHistoryDatabaseProxy.getUniqueChats({
        limit: 10,
        order_direction: 'DESC'
      })

      if (result.success && result.data.length > 0) {
        // 更新历史数据并显示气泡
        this.historyBubble.updateHistory(result.data)

        // 获取悬浮气泡作为锚点
        const floatingBubble = document.getElementById('echosync-floating-bubble')
        if (floatingBubble) {
          this.historyBubble.show(floatingBubble)
          console.log('History bubble shown with', result.data.length, 'items')
        }
      } else {
        console.log('No history data to show')
      }
    } catch (error) {
      console.error('Error showing history bubble:', error)
    }
  }

  /**
   * 处理历史项点击
   */
  private handleHistoryItemClick(chat: ChatHistoryWithPlatformAndPrompt): void {
    console.log('【EchoSync】History item clicked:', chat)

    // 触发提示词注入事件，同时传递 chat_uid 用于跨平台共享
    document.dispatchEvent(new CustomEvent('echosync:inject-prompt', {
      detail: {
        prompt: chat.chat_prompt,
        chat_uid: chat.chat_uid
      }
    }))

    // 隐藏历史气泡
    if (this.historyBubble) {
      this.historyBubble.hide()
    }
  }

  /**
   * 显示存储的提示词
   */
  async showStoredPrompts(): Promise<void> {
    try {
      // 使用新的getPromptsWithPlatforms方法获取提示词列表
      const result = await chatHistoryDatabaseProxy.getPromptsWithPlatforms({
        limit: 20,
        order_direction: 'DESC'
      })

      if (result.success) {
        console.group('【EchoSync】=== 存储的提示词 ===')
        result.data.forEach((item: any, index: number) => {
          const platformNames = item.platforms.map((p: any) => p.platform_name).join(', ')
          console.log('【EchoSync】' + `${index + 1}. [${new Date(item.create_time).toLocaleString()}] ${item.chat_prompt} (平台: ${platformNames})`)
        })
        console.log('【EchoSync】总计:', result.data.length, '条记录')
        console.groupEnd()
      } else {
        console.error('【EchoSync】获取存储的提示词失败:', result.error)
      }
    } catch (error) {
      console.error('【EchoSync】显示存储的提示词时出错:', error)
    }
  }

  /**
   * 检查鼠标是否在历史气泡上
   */
  isMouseOverHistoryBubble(): boolean {
    if (!this.historyBubble) return false
    return this.historyBubble.visible
  }

  /**
   * 隐藏历史气泡
   */
  hideHistoryBubble(): void {
    if (this.historyBubble) {
      this.historyBubble.hide()
    }
  }

  /**
   * 获取历史气泡
   */
  getHistoryBubble(): HistoryBubble | null {
    return this.historyBubble
  }

  /**
   * 销毁
   */
  destroy(): void {
    if (this.historyBubble) {
      this.historyBubble.destroy()
      this.historyBubble = null
    }

    // 移除事件监听器
    document.removeEventListener('echosync:history-item-click', this.handleHistoryItemClick.bind(this))
    document.removeEventListener('echosync:request-history-data', this.showHistoryBubble.bind(this))
  }
}
