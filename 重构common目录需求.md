common的结构现在不合理，common的主要作用是提供对数据库的操作。
准备借鉴springboot的 service与dao结构进行改造。
## 1. 数据结构

common/types中的database_entity.ts,应该改造为纯数据库表的对象关系映射，数据库表结构参见`common/database/dexie.ts`,不要有其他逻辑。
common/types中的database_dto.ts,应为数据传输对象，用于其他模块，或者ui与service数据传输，因为dao只能获得entity对象，要在service中对entity进行转换，返回dto


## 2. 服务结构
应该把现在的`common/dao`中的三个文件 以及`data/database/dexie.ts`,梳理一下，拆分为
1. `dexie`只维护与数据库的连接，不要有业务逻辑
2. `dao`目录，只负责对数据库的crud，sql语句拼接，不要有业务逻辑
3. `service`目录，负责调用dao类的方法，从数据库查询结果，再封装为dto,`service`目录中的类的方法，只能被background中的