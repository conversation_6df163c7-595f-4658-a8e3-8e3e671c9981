import { dexieDatabase } from '../database/dexie'
import { ChatPrompt } from '@/common/types/database_entity'
import {
  CreateChatPromptInput,
  UpdateChatPromptInput,
  ChatPromptQueryParams,
  PaginatedResult,
  SearchResult,
  DatabaseResult
} from '@/common/types/database_dto'

export class ChatPromptService {
  private static instance: ChatPromptService
  private promptCache: Map<string, ChatPrompt> = new Map()
  private cacheExpiry: Map<string, number> = new Map()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存

  public static getInstance(): ChatPromptService {
    if (!ChatPromptService.instance) {
      ChatPromptService.instance = new ChatPromptService()
    }
    return ChatPromptService.instance
  }

  /**
   * 创建聊天提示词记录
   */
  async create(input: CreateChatPromptInput): Promise<DatabaseResult<ChatPrompt>> {
    try {
      console.log('【EchoSync】Starting create chat prompt with input:', input)
      await dexieDatabase.initialize()
      console.log('【EchoSync】Database initialized successfully')

      const now = Date.now()
      const data: Omit<ChatPrompt, 'id'> = {
        chat_prompt: input.chat_prompt,
        chat_uid: input.chat_uid || now.toString(),
        create_time: input.create_time || now,
        is_synced: 0,
        is_delete: 0
      }

      const id = await dexieDatabase.chatPrompt.add(data)
      console.log('【EchoSync】Chat prompt created with id:', id)

      return {
        success: true,
        data: { ...data, id: id as number }
      }
    } catch (error) {
      console.error('Create chat prompt error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据ID获取聊天提示词记录
   */
  async getById(id: number): Promise<DatabaseResult<ChatPrompt>> {
    try {
      await dexieDatabase.initialize()
      
      const record = await dexieDatabase.chatPrompt
        .where('id')
        .equals(id)
        .and(item => item.is_delete === 0)
        .first()

      if (!record) {
        return {
          success: false,
          error: 'Record not found'
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Get chat prompt by id error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 更新聊天提示词记录
   */
  async update(id: number, input: UpdateChatPromptInput): Promise<DatabaseResult<ChatPrompt>> {
    try {
      await dexieDatabase.initialize()
      
      await dexieDatabase.chatPrompt.update(id, input)
      const record = await dexieDatabase.chatPrompt.get(id)

      if (!record) {
        return {
          success: false,
          error: 'Record not found after update'
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Update chat prompt error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 删除聊天提示词记录（软删除）
   */
  async delete(id: number): Promise<DatabaseResult<boolean>> {
    try {
      await dexieDatabase.initialize()
      
      await dexieDatabase.chatPrompt.update(id, { is_delete: 1 })
      
      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Delete chat prompt error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取聊天提示词列表（带分页）
   */
  async getList(params: ChatPromptQueryParams = {}): Promise<DatabaseResult<PaginatedResult<ChatPrompt>>> {
    try {
      await dexieDatabase.initialize()
      
      const {
        page = 1,
        limit = 20,
        order_by = 'create_time',
        order_direction = 'DESC'
      } = params

      const offset = (page - 1) * limit

      let query = dexieDatabase.chatPrompt.where('is_delete').equals(0)

      const records = await query
        .offset(offset)
        .limit(limit)
        .reverse() // Dexie 中的倒序
        .toArray()

      // 获取总数
      const total = await query.count()

      return {
        success: true,
        data: {
          data: records,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      console.error('Get chat prompt list error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 搜索聊天提示词
   */
  async search(searchTerm: string, params: ChatPromptQueryParams = {}): Promise<DatabaseResult<SearchResult<ChatPrompt>>> {
    try {
      await dexieDatabase.initialize()
      
      const {
        page = 1,
        limit = 20
      } = params

      const offset = (page - 1) * limit

      const results = await dexieDatabase.chatPrompt
        .where('is_delete')
        .equals(0)
        .filter(item => 
          item.chat_prompt.toLowerCase().includes(searchTerm.toLowerCase())
        )
        .toArray()

      // 手动分页
      const paginatedResults = results.slice(offset, offset + limit)

      return {
        success: true,
        data: {
          data: paginatedResults,
          total: results.length,
          page,
          limit,
          totalPages: Math.ceil(results.length / limit),
          searchTerm
        }
      }
    } catch (error) {
      console.error('Search chat prompt error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据提示词内容查找记录（带缓存优化）
   */
  async findByPrompt(prompt: string): Promise<DatabaseResult<ChatPrompt | null>> {
    try {
      // 检查缓存
      const cached = this.getCachedPrompt(prompt)
      if (cached) {
        console.log('【EchoSync】Found cached prompt for:', prompt.substring(0, 50) + '...')
        return {
          success: true,
          data: cached
        }
      }

      await dexieDatabase.initialize()

      const result = await dexieDatabase.chatPrompt
        .where('chat_prompt')
        .equals(prompt)
        .and(item => item.is_delete === 0)
        .first()

      // 缓存结果
      if (result) {
        this.setCachedPrompt(prompt, result)
      }

      return {
        success: true,
        data: result || null
      }
    } catch (error) {
      console.error('Find chat prompt by prompt error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取缓存的提示词
   */
  private getCachedPrompt(prompt: string): ChatPrompt | null {
    const expiry = this.cacheExpiry.get(prompt)
    if (!expiry || Date.now() > expiry) {
      // 缓存过期，清理
      this.promptCache.delete(prompt)
      this.cacheExpiry.delete(prompt)
      return null
    }
    return this.promptCache.get(prompt) || null
  }

  /**
   * 设置缓存的提示词
   */
  private setCachedPrompt(prompt: string, chatPrompt: ChatPrompt): void {
    this.promptCache.set(prompt, chatPrompt)
    this.cacheExpiry.set(prompt, Date.now() + this.CACHE_TTL)
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache(): void {
    const now = Date.now()
    for (const [prompt, expiry] of this.cacheExpiry.entries()) {
      if (now > expiry) {
        this.promptCache.delete(prompt)
        this.cacheExpiry.delete(prompt)
      }
    }
  }

  /**
   * 根据chat_uid查找记录
   */
  async findByChatUid(chatUid: string): Promise<DatabaseResult<ChatPrompt | null>> {
    try {
      await dexieDatabase.initialize()

      const result = await dexieDatabase.chatPrompt
        .where('chat_uid')
        .equals(chatUid)
        .and(item => item.is_delete === 0)
        .first()

      return {
        success: true,
        data: result || null
      }
    } catch (error) {
      console.error('Find chat prompt by chat_uid error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}

// 导出单例实例
export const chatPromptService = ChatPromptService.getInstance()
